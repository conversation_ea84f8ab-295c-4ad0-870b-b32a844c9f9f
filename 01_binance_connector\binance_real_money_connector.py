#!/usr/bin/env python3
"""
Binance Real Money Trading Connector
100% compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md
Full isolated margin trading implementation for live Bitcoin trading
"""

import json
import time
import logging
from datetime import datetime, timedelta
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceOrderException
import pandas as pd
import numpy as np
import sys
import os

# Add shared config path
sys.path.append('../shared_config')
sys.path.append('shared_config')
try:
    from secure_credentials import get_binance_credentials, get_trading_config
except ImportError:
    # Fallback for different path structures
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared_config'))
    from secure_credentials import get_binance_credentials, get_trading_config

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BinanceRealMoneyConnector:
    """Comprehensive Binance connector for real money trading"""
    
    def __init__(self):
        self.load_config()
        self.client = self.initialize_client()
        self.symbol = 'BTCUSDT'
        self.margin_type = 'ISOLATED'
        self.leverage = 10
        self.active_orders = {}
        self.position_info = None
        
    def load_config(self):
        """Load Binance configuration securely from environment variables"""
        try:
            # Load credentials securely from environment
            binance_creds = get_binance_credentials()
            self.api_key = binance_creds['api_key']
            self.api_secret = binance_creds['api_secret']

            # Load trading configuration
            self.trading_config = get_trading_config()

            logger.info("✅ Binance configuration loaded securely from environment")

        except Exception as e:
            logger.error(f"❌ Failed to load Binance configuration: {e}")
            logger.error("💡 Make sure to set BINANCE_API_KEY and BINANCE_API_SECRET in .env file")
            raise
    
    def initialize_client(self):
        """Initialize Binance client with isolated margin"""
        try:
            client = Client(self.api_key, self.api_secret)

            # Test connection
            account_info = client.get_account()
            logger.info("✅ Binance client connected successfully")

            # Assign client first, then setup isolated margin
            self.client = client
            self.setup_isolated_margin()

            return client

        except Exception as e:
            logger.error(f"❌ Failed to initialize Binance client: {e}")
            raise
    
    def setup_isolated_margin(self):
        """Setup isolated margin trading for BTCUSDT with auto-management"""
        try:
            logger.info("🔧 Setting up isolated margin for live trading...")

            # Check if isolated margin account already exists
            try:
                existing_account = self.client.get_isolated_margin_account()
                btc_usdt_exists = False

                for asset in existing_account.get('assets', []):
                    if asset.get('symbol') == self.symbol:
                        btc_usdt_exists = True
                        logger.info("✅ Isolated margin account already exists for BTCUSDT")
                        break

                if not btc_usdt_exists:
                    # Create isolated margin account
                    self.client.create_isolated_margin_account(
                        base='BTC',
                        quote='USDT'
                    )
                    logger.info("✅ Isolated margin account created for BTCUSDT")

            except Exception as e:
                # If account doesn't exist or other error, try to create
                try:
                    self.client.create_isolated_margin_account(
                        base='BTC',
                        quote='USDT'
                    )
                    logger.info("✅ Isolated margin account created for BTCUSDT")
                except Exception as create_error:
                    if 'already exists' in str(create_error).lower() or 'duplicate' in str(create_error).lower():
                        logger.info("✅ Isolated margin account already exists for BTCUSDT")
                    else:
                        logger.warning(f"⚠️ Isolated margin setup issue: {create_error}")
                        # Continue anyway - account might be ready

            # Set leverage (with error handling for already set leverage)
            try:
                self.client.change_margin_leverage(
                    symbol=self.symbol,
                    leverage=self.leverage,
                    isIsolated=True
                )
                logger.info(f"✅ Leverage set to {self.leverage}x for isolated margin")
            except Exception as leverage_error:
                if 'leverage not modified' in str(leverage_error).lower():
                    logger.info(f"✅ Leverage already set to {self.leverage}x for isolated margin")
                else:
                    logger.warning(f"⚠️ Leverage setting issue: {leverage_error}")
                    # Continue anyway - leverage might already be correct

            logger.info("✅ Isolated margin setup completed for live trading")

        except Exception as e:
            logger.error(f"❌ Failed to setup isolated margin: {e}")
            # Don't raise - allow system to continue with regular trading
            logger.info("⚠️ Continuing with regular trading mode")
    
    def get_account_balance(self):
        """Get isolated margin account balance"""
        try:
            account = self.client.get_isolated_margin_account()
            
            # Find BTCUSDT isolated margin balance
            for asset in account['assets']:
                if asset['symbol'] == 'BTCUSDT':
                    usdt_balance = {
                        'free': float(asset['quoteAsset']['free']),
                        'locked': float(asset['quoteAsset']['locked']),
                        'borrowed': float(asset['quoteAsset']['borrowed']),
                        'interest': float(asset['quoteAsset']['interest']),
                        'netAsset': float(asset['quoteAsset']['netAsset'])
                    }
                    
                    btc_balance = {
                        'free': float(asset['baseAsset']['free']),
                        'locked': float(asset['baseAsset']['locked']),
                        'borrowed': float(asset['baseAsset']['borrowed']),
                        'interest': float(asset['baseAsset']['interest']),
                        'netAsset': float(asset['baseAsset']['netAsset'])
                    }
                    
                    current_price = self.get_current_price()
                    total_usdt_value = usdt_balance['netAsset'] + (btc_balance['netAsset'] * current_price)
                    
                    return {
                        'usdt': usdt_balance,
                        'btc': btc_balance,
                        'total_usdt_value': total_usdt_value,
                        'current_btc_price': current_price,
                        'margin_level': float(asset['marginLevel']),
                        'margin_ratio': float(asset['marginRatio']),
                        'index_price': float(asset['indexPrice']),
                        'liquidate_price': float(asset['liquidatePrice']) if asset['liquidatePrice'] else None
                    }
            
            logger.error("❌ BTCUSDT isolated margin account not found")
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get account balance: {e}")
            return None

    def get_account_info(self):
        """Get general account information"""
        try:
            # Get spot account info
            spot_account = self.client.get_account()

            # Get isolated margin account info
            try:
                margin_account = self.client.get_isolated_margin_account()
                margin_enabled = True
            except Exception as e:
                logger.warning(f"Isolated margin not available: {e}")
                margin_account = None
                margin_enabled = False

            account_info = {
                'account_type': 'SPOT_AND_MARGIN' if margin_enabled else 'SPOT_ONLY',
                'can_trade': spot_account.get('canTrade', False),
                'can_withdraw': spot_account.get('canWithdraw', False),
                'can_deposit': spot_account.get('canDeposit', False),
                'update_time': spot_account.get('updateTime', 0),
                'account_type_detail': 'Isolated Margin Enabled' if margin_enabled else 'Spot Trading Only',
                'permissions': spot_account.get('permissions', []),
                'isolated_margin_enabled': margin_enabled,
                'margin_account': margin_account
            }

            logger.info(f"✅ Account info retrieved - Type: {account_info['account_type']}")
            return account_info

        except Exception as e:
            logger.error(f"❌ Failed to get account info: {e}")
            return None

    def get_current_price(self):
        """Get current BTC price"""
        try:
            ticker = self.client.get_symbol_ticker(symbol=self.symbol)
            return float(ticker['price'])
        except Exception as e:
            logger.error(f"❌ Failed to get current price: {e}")
            return None
    
    def get_market_data(self, interval='30m', limit=100):
        """Get comprehensive market data for analysis"""
        try:
            # Get klines data
            klines = self.client.get_klines(
                symbol=self.symbol,
                interval=interval,
                limit=limit
            )
            
            # Convert to DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert to numeric
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col])
            
            # Calculate technical indicators
            df = self.calculate_indicators(df)
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to get market data: {e}")
            return None
    
    def calculate_indicators(self, df):
        """Calculate RSI and VWAP indicators"""
        try:
            # Calculate RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # Calculate VWAP
            typical_price = (df['high'] + df['low'] + df['close']) / 3
            df['vwap'] = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate indicators: {e}")
            return df
    
    def calculate_position_size(self, entry_price, stop_loss_price, risk_amount):
        """Calculate position size for AUTO-REBALANCING SYSTEM - Fixed $1.00 SL, $2.50 TP with automatic scaling"""
        try:
            # Get current account balance for auto-rebalancing
            balance_info = self.get_account_balance()
            current_balance = balance_info.get('total_usdt_value', 0.0)

            # AUTO-REBALANCING SYSTEM: Fixed dollar amounts that scale with account growth
            target_risk_amount = 1.00    # Always $1.00 stop loss
            target_reward_amount = 2.50  # Always $2.50 take profit
            target_ratio = target_reward_amount / target_risk_amount  # 2.5:1

            logger.info(f"🔄 AUTO-REBALANCING SYSTEM: Current balance ${current_balance:.2f}")
            logger.info(f"🎯 TARGET AMOUNTS: Risk ${target_risk_amount:.2f} | Reward ${target_reward_amount:.2f} | Ratio {target_ratio:.1f}:1")

            # From master document: SL = 0.1%, TP = 0.25% gives 2.5:1 ratio
            stop_loss_distance = 0.001   # 0.1%
            take_profit_distance = 0.0025  # 0.25%

            # Calculate position size for exactly $1.00 risk
            # Risk = Position Size × Entry Price × SL Distance
            required_position_btc = target_risk_amount / (entry_price * stop_loss_distance)

            # Verify this gives us exactly $2.50 reward
            verified_reward = required_position_btc * entry_price * take_profit_distance
            verified_ratio = verified_reward / target_risk_amount

            logger.info(f"💰 FIXED DOLLAR CALCULATION:")
            logger.info(f"💰 Required Position: {required_position_btc:.8f} BTC")
            logger.info(f"💰 Verified Reward: ${verified_reward:.2f}")
            logger.info(f"💰 Verified Ratio: {verified_ratio:.1f}:1")

            # Calculate position value
            position_value = required_position_btc * entry_price

            # AUTO-REBALANCING: Check if position fits in account
            position_percentage = (position_value / current_balance) * 100

            # If position is too large for account, scale down but maintain ratio
            max_position_percentage = 80.0  # Allow up to 80% of account for auto-rebalancing

            if position_percentage > max_position_percentage:
                # Scale down position to fit account
                max_position_value = current_balance * (max_position_percentage / 100)
                scaled_position_btc = max_position_value / entry_price

                # Calculate scaled risk and reward (maintaining 2.5:1 ratio)
                scaled_risk = scaled_position_btc * entry_price * stop_loss_distance
                scaled_reward = scaled_position_btc * entry_price * take_profit_distance
                scaled_ratio = scaled_reward / scaled_risk

                logger.info(f"🔄 AUTO-REBALANCING: Scaling down to fit account")
                logger.info(f"🔄 SCALED POSITION: {scaled_position_btc:.8f} BTC (${max_position_value:.2f})")
                logger.info(f"🔄 SCALED RISK: ${scaled_risk:.2f} | SCALED REWARD: ${scaled_reward:.2f}")
                logger.info(f"🔄 SCALED RATIO: {scaled_ratio:.1f}:1 (ratio maintained)")

                final_position_btc = scaled_position_btc
                final_risk = scaled_risk
                final_reward = scaled_reward
                final_ratio = scaled_ratio
                final_position_value = max_position_value
                rebalanced = True
            else:
                # Use exact $1.00/$2.50 amounts
                final_position_btc = required_position_btc
                final_risk = target_risk_amount
                final_reward = target_reward_amount
                final_ratio = target_ratio
                final_position_value = position_value
                rebalanced = False

            # Format to proper precision for Binance LOT_SIZE filter
            formatted_position = round(final_position_btc, 5)

            # Ensure minimum quantity (0.00001 BTC)
            min_quantity = 0.00001
            if formatted_position < min_quantity:
                formatted_position = min_quantity
                actual_risk = formatted_position * entry_price * stop_loss_distance
                actual_reward = formatted_position * entry_price * take_profit_distance
                actual_ratio = actual_reward / actual_risk
                logger.info(f"⚠️ Using minimum quantity: {min_quantity:.5f} BTC")
            else:
                actual_risk = final_risk
                actual_reward = final_reward
                actual_ratio = final_ratio

            # Final calculations
            final_position_value = formatted_position * entry_price
            final_position_percentage = (final_position_value / current_balance) * 100

            logger.info(f"🚀 AUTO-REBALANCING FINAL:")
            logger.info(f"🚀 Position Size: {formatted_position:.5f} BTC")
            logger.info(f"🚀 Position Value: ${final_position_value:.2f} ({final_position_percentage:.1f}% of account)")
            logger.info(f"🚀 Risk: ${actual_risk:.2f} | Reward: ${actual_reward:.2f} | Ratio: {actual_ratio:.1f}:1")
            logger.info(f"🚀 Rebalanced: {'YES' if rebalanced else 'NO (exact $1/$2.50)'}")

            return {
                'position_size_btc': formatted_position,
                'position_size_usdt': final_position_value,
                'risk_amount': actual_risk,
                'reward_amount': actual_reward,
                'risk_reward_ratio': actual_ratio,
                'current_balance': current_balance,
                'position_percentage': final_position_percentage,
                'rebalanced': rebalanced,
                'target_risk': target_risk_amount,
                'target_reward': target_reward_amount,
                'leverage_used': 1  # No leverage for safety
            }

        except Exception as e:
            logger.error(f"❌ Failed to calculate auto-rebalancing position size: {e}")
            return None
    
    def place_market_order(self, side, quantity):
        """Place market order for immediate execution"""
        try:
            order = self.client.create_margin_order(
                symbol=self.symbol,
                side=side,
                type='MARKET',
                quantity=quantity,
                isIsolated=True
            )
            
            logger.info(f"✅ Market order placed: {order['orderId']} - {side} {quantity} BTC")
            
            return {
                'order_id': order['orderId'],
                'symbol': order['symbol'],
                'side': order['side'],
                'quantity': float(order['origQty']),
                'price': float(order.get('price', 0)),
                'status': order['status'],
                'timestamp': datetime.now().isoformat(),
                'fills': order.get('fills', [])
            }
            
        except BinanceOrderException as e:
            logger.error(f"❌ Order placement failed: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Unexpected error placing order: {e}")
            return None
    
    def place_stop_loss_order(self, side, quantity, stop_price):
        """Place stop loss order"""
        try:
            order = self.client.create_margin_order(
                symbol=self.symbol,
                side=side,
                type='STOP_LOSS_LIMIT',
                quantity=quantity,
                price=stop_price,
                stopPrice=stop_price,
                timeInForce='GTC',
                isIsolated=True
            )
            
            logger.info(f"✅ Stop loss order placed: {order['orderId']} at ${stop_price}")
            
            return {
                'order_id': order['orderId'],
                'type': 'STOP_LOSS',
                'stop_price': stop_price,
                'quantity': quantity,
                'side': side
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to place stop loss order: {e}")
            return None
    
    def place_take_profit_order(self, side, quantity, target_price):
        """Place take profit order"""
        try:
            order = self.client.create_margin_order(
                symbol=self.symbol,
                side=side,
                type='LIMIT',
                quantity=quantity,
                price=target_price,
                timeInForce='GTC',
                isIsolated=True
            )
            
            logger.info(f"✅ Take profit order placed: {order['orderId']} at ${target_price}")
            
            return {
                'order_id': order['orderId'],
                'type': 'TAKE_PROFIT',
                'target_price': target_price,
                'quantity': quantity,
                'side': side
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to place take profit order: {e}")
            return None

    def place_oco_order(self, side, quantity, stop_price, limit_price):
        """Place OCO (One-Cancels-Other) order for stop loss and take profit"""
        try:
            # Format quantity to proper precision
            formatted_quantity = f"{quantity:.5f}"

            # Place OCO order
            oco_order = self.client.create_margin_oco_order(
                symbol=self.symbol,
                side=side,
                quantity=formatted_quantity,
                price=f"{limit_price:.2f}",  # Take profit price
                stopPrice=f"{stop_price:.2f}",  # Stop loss trigger price
                stopLimitPrice=f"{stop_price:.2f}",  # Stop loss limit price
                stopLimitTimeInForce='GTC',
                isIsolated=True
            )

            logger.info(f"✅ OCO order placed: {oco_order['orderListId']} - {side} {formatted_quantity} BTC")
            logger.info(f"📈 Take profit: ${limit_price:.2f}")
            logger.info(f"🛑 Stop loss: ${stop_price:.2f}")

            return {
                'order_list_id': oco_order['orderListId'],
                'symbol': oco_order['symbol'],
                'side': side,
                'quantity': float(formatted_quantity),
                'limit_price': limit_price,
                'stop_price': stop_price,
                'status': oco_order['listStatusType'],
                'orders': oco_order['orders'],
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"❌ Failed to place OCO order: {e}")
            # Fallback to separate orders if OCO fails
            logger.info("🔄 Falling back to separate SL/TP orders...")

            stop_order = self.place_stop_loss_order(side, quantity, stop_price)
            tp_order = self.place_take_profit_order(side, quantity, limit_price)

            return {
                'fallback': True,
                'stop_order': stop_order,
                'tp_order': tp_order,
                'timestamp': datetime.now().isoformat()
            }

    def execute_full_trade(self, signal, entry_price, confidence):
        """Execute complete trade with entry, stop loss, and take profit"""
        try:
            # Get account balance
            balance = self.get_account_balance()
            if not balance:
                logger.error("❌ Failed to get account balance")
                return None
            
            # Fixed risk amount: $1 per trade for testing
            risk_amount = 1.0
            
            # Calculate stop loss and take profit prices
            if signal == 'BUY':
                stop_loss_price = entry_price * 0.999  # 0.1% stop loss
                take_profit_price = entry_price * (1 + (0.001 * 2.5))  # 2.5:1 RR
                market_side = 'BUY'
                exit_side = 'SELL'
            else:  # SELL
                stop_loss_price = entry_price * 1.001  # 0.1% stop loss
                take_profit_price = entry_price * (1 - (0.001 * 2.5))  # 2.5:1 RR
                market_side = 'SELL'
                exit_side = 'BUY'
            
            # Calculate position size
            position_info = self.calculate_position_size(entry_price, stop_loss_price, risk_amount)
            if not position_info:
                logger.error("❌ Failed to calculate position size")
                return None
            
            # Execute market order
            market_order = self.place_market_order(market_side, position_info['position_size_btc'])
            if not market_order:
                logger.error("❌ Failed to execute market order")
                return None
            
            # Place OCO order (One-Cancels-Other) for stop loss and take profit
            oco_order = self.place_oco_order(
                exit_side,
                position_info['position_size_btc'],
                stop_loss_price,
                take_profit_price
            )
            
            # Store trade information
            trade_info = {
                'signal': signal,
                'entry_order': market_order,
                'oco_order': oco_order,
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'position_size': position_info['position_size_btc'],
                'risk_amount': risk_amount,
                'confidence': confidence,
                'timestamp': datetime.now().isoformat(),
                'status': 'ACTIVE'
            }
            
            self.position_info = trade_info
            
            logger.info(f"✅ Full trade executed: {signal} at ${entry_price}")
            logger.info(f"📊 Position size: {position_info['position_size_btc']} BTC")
            logger.info(f"🛑 Stop loss: ${stop_loss_price}")
            logger.info(f"🎯 Take profit: ${take_profit_price}")
            
            return trade_info
            
        except Exception as e:
            logger.error(f"❌ Failed to execute full trade: {e}")
            return None

    def place_limit_order(self, side, quantity, price, stop_loss_price, take_profit_price):
        """Place limit order at exact grid level with stop loss and take profit"""
        try:
            logger.info(f"🎯 Placing limit {side} order at ${price:.2f}")

            # Format quantity to proper precision
            formatted_quantity = f"{quantity:.5f}"
            formatted_price = f"{price:.2f}"

            # Place limit order
            limit_order = self.client.create_margin_order(
                symbol=self.symbol,
                side=side,
                type='LIMIT',
                quantity=formatted_quantity,
                price=formatted_price,
                timeInForce='GTC',
                isIsolated=True
            )

            if not limit_order:
                logger.error("❌ Limit order placement failed")
                return None

            logger.info(f"✅ Limit order placed: {limit_order['orderId']}")
            logger.info(f"⏳ Waiting for limit order to fill before placing TP/SL...")

            # Return limit order info - OCO will be placed when limit order fills
            return {
                'order_id': limit_order['orderId'],
                'type': f'LIMIT_{side}',
                'entry_price': price,
                'stop_loss': stop_loss_price,
                'take_profit': take_profit_price,
                'quantity': quantity,
                'side': side,
                'limit_order': limit_order,
                'status': 'PENDING_FILL',
                'oco_order': None,  # Will be placed after limit order fills
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"❌ Failed to place limit order: {e}")
            return None

    def monitor_and_place_oco_on_fill(self, limit_order_info):
        """Monitor limit order and place OCO when it fills"""
        try:
            order_id = limit_order_info['order_id']
            side = limit_order_info['side']
            quantity = limit_order_info['quantity']
            stop_loss_price = limit_order_info['stop_loss']
            take_profit_price = limit_order_info['take_profit']

            logger.info(f"🔍 Monitoring limit order {order_id} for fill...")

            # Monitor order status
            max_checks = 60  # Check for up to 60 iterations (5 minutes if checking every 5 seconds)
            check_count = 0

            while check_count < max_checks:
                try:
                    # Check order status
                    order_status = self.client.get_margin_order(
                        symbol=self.symbol,
                        orderId=order_id,
                        isIsolated=True
                    )

                    if order_status['status'] == 'FILLED':
                        logger.info(f"✅ Limit order {order_id} FILLED! Placing OCO order...")

                        # Get actual fill price and quantity
                        fill_price = float(order_status['price'])
                        fill_quantity = float(order_status['executedQty'])

                        # Place OCO order for the filled position
                        exit_side = 'SELL' if side == 'BUY' else 'BUY'

                        oco_order = self.place_oco_order(
                            exit_side,
                            fill_quantity,
                            stop_loss_price,
                            take_profit_price
                        )

                        if oco_order:
                            logger.info(f"✅ OCO order placed successfully after limit fill")
                            return {
                                'limit_filled': True,
                                'fill_price': fill_price,
                                'fill_quantity': fill_quantity,
                                'oco_order': oco_order,
                                'timestamp': datetime.now().isoformat()
                            }
                        else:
                            logger.error("❌ Failed to place OCO after limit fill")
                            return {'limit_filled': True, 'oco_placed': False}

                    elif order_status['status'] in ['CANCELED', 'REJECTED', 'EXPIRED']:
                        logger.warning(f"⚠️ Limit order {order_id} status: {order_status['status']}")
                        return {'limit_filled': False, 'status': order_status['status']}

                    # Order still pending, wait and check again
                    time.sleep(5)  # Check every 5 seconds
                    check_count += 1

                except Exception as e:
                    logger.error(f"❌ Error checking order status: {e}")
                    time.sleep(5)
                    check_count += 1

            logger.warning(f"⏰ Timeout monitoring limit order {order_id}")
            return {'limit_filled': False, 'status': 'TIMEOUT'}

        except Exception as e:
            logger.error(f"❌ Failed to monitor limit order: {e}")
            return {'error': str(e)}
    
    def get_open_orders(self):
        """Get all open orders"""
        try:
            orders = self.client.get_open_margin_orders(
                symbol=self.symbol,
                isIsolated=True
            )
            return orders
        except Exception as e:
            logger.error(f"❌ Failed to get open orders: {e}")
            return []
    
    def cancel_all_orders(self):
        """Cancel all open orders"""
        try:
            result = self.client.cancel_open_margin_orders(
                symbol=self.symbol,
                isIsolated=True
            )
            logger.info("✅ All open orders cancelled")
            return result
        except Exception as e:
            logger.error(f"❌ Failed to cancel orders: {e}")
            return None
    
    def get_trade_history(self, limit=50):
        """Get recent trade history"""
        try:
            trades = self.client.get_margin_trades(
                symbol=self.symbol,
                isIsolated=True,
                limit=limit
            )
            return trades
        except Exception as e:
            logger.error(f"❌ Failed to get trade history: {e}")
            return []
    
    def close_position(self):
        """Close current position immediately"""
        try:
            if not self.position_info:
                logger.warning("⚠️ No active position to close")
                return True
            
            # Cancel existing orders
            self.cancel_all_orders()
            
            # Get current position
            balance = self.get_account_balance()
            btc_position = balance['btc']['netAsset']
            
            if abs(btc_position) > 0.000001:  # Minimum position size
                # Determine side to close position
                close_side = 'SELL' if btc_position > 0 else 'BUY'
                
                # Close position with market order
                close_order = self.place_market_order(close_side, abs(btc_position))
                
                if close_order:
                    logger.info(f"✅ Position closed: {close_side} {abs(btc_position)} BTC")
                    self.position_info = None
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Failed to close position: {e}")
            return False
    
    def emergency_shutdown(self):
        """Emergency shutdown - close all positions and cancel orders"""
        try:
            logger.critical("🚨 EMERGENCY SHUTDOWN INITIATED")
            
            # Cancel all orders
            self.cancel_all_orders()
            
            # Close all positions
            self.close_position()
            
            logger.critical("🚨 Emergency shutdown completed")
            return True
            
        except Exception as e:
            logger.critical(f"❌ Emergency shutdown failed: {e}")
            return False

# Global connector instance
binance_connector = BinanceRealMoneyConnector()

def get_binance_connector():
    """Get global Binance connector instance"""
    return binance_connector

if __name__ == "__main__":
    # Test connection
    connector = BinanceRealMoneyConnector()
    
    print("🔗 Binance Real Money Connector Test")
    print("=" * 40)
    
    # Test balance
    balance = connector.get_account_balance()
    if balance:
        print(f"💰 Total USDT Value: ${balance['total_usdt_value']:.2f}")
        print(f"📊 BTC Position: {balance['btc']['netAsset']:.6f}")
        print(f"💵 USDT Balance: {balance['usdt']['netAsset']:.2f}")
    
    # Test market data
    market_data = connector.get_market_data()
    if market_data is not None:
        current_price = market_data['close'].iloc[-1]
        current_rsi = market_data['rsi'].iloc[-1]
        current_vwap = market_data['vwap'].iloc[-1]
        
        print(f"📈 Current Price: ${current_price:.2f}")
        print(f"📊 RSI: {current_rsi:.2f}")
        print(f"📈 VWAP: ${current_vwap:.2f}")
    
    print("✅ Binance connector ready for real money trading")
