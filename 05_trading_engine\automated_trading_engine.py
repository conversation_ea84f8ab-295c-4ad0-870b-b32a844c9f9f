#!/usr/bin/env python3
"""
Automated Trading Engine
100% compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md
Grid-aware trading with 2.5:1 risk-reward ratio enforcement
"""

import json
import time
import logging
import threading
from datetime import datetime
from binance.client import Client
from binance.exceptions import BinanceAPIException
import requests
from binance_real_money_connector import get_binance_connector

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AutomatedTradingEngine:
    """Automated trading engine with grid-aware execution"""
    
    def __init__(self):
        self.load_config()
        self.binance_connector = get_binance_connector()
        self.running = False
        self.current_position = None
        self.daily_trades = 0
        self.max_daily_trades = 8
        self.risk_per_trade = 0.01  # 1%
        self.risk_reward_ratio = 2.5
        
    def load_config(self):
        """Load configuration files"""
        try:
            with open('binance_isolated_margin_config.json', 'r') as f:
                self.binance_config = json.load(f)
            
            with open('money_management_config.json', 'r') as f:
                self.money_config = json.load(f)
                
            with open('telegram_config.json', 'r') as f:
                self.telegram_config = json.load(f)
                
            logger.info("✅ Trading engine configuration loaded")
        except Exception as e:
            logger.error(f"❌ Failed to load configuration: {e}")
            raise
    
    def setup_binance(self):
        """Setup Binance client for isolated margin trading"""
        try:
            client = Client(
                self.binance_config['binance_config']['api_key'],
                self.binance_config['binance_config']['api_secret']
            )
            logger.info("✅ Binance trading client initialized")
            return client
        except Exception as e:
            logger.error(f"❌ Failed to setup Binance client: {e}")
            raise
    
    def get_account_balance(self):
        """Get isolated margin account balance"""
        try:
            account = self.binance_client.get_isolated_margin_account()
            
            # Find BTCUSDT isolated margin balance
            for asset in account['assets']:
                if asset['symbol'] == 'BTCUSDT':
                    usdt_balance = float(asset['quoteAsset']['free'])
                    btc_balance = float(asset['baseAsset']['free'])
                    return {
                        'usdt_balance': usdt_balance,
                        'btc_balance': btc_balance,
                        'total_usdt_value': usdt_balance + (btc_balance * self.get_current_price())
                    }
            
            logger.error("❌ BTCUSDT isolated margin account not found")
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get account balance: {e}")
            return None
    
    def get_current_price(self):
        """Get current BTC price"""
        try:
            ticker = self.binance_client.get_symbol_ticker(symbol='BTCUSDT')
            return float(ticker['price'])
        except Exception as e:
            logger.error(f"❌ Failed to get current price: {e}")
            return None
    
    def calculate_position_size(self, entry_price, stop_loss_price):
        """Calculate position size based on 1% risk"""
        try:
            balance = self.get_account_balance()
            if not balance:
                return None
            
            account_value = balance['total_usdt_value']
            risk_amount = account_value * self.risk_per_trade
            
            # Calculate position size based on stop loss distance
            price_difference = abs(entry_price - stop_loss_price)
            position_size_usdt = risk_amount / (price_difference / entry_price)
            position_size_btc = position_size_usdt / entry_price
            
            return {
                'position_size_btc': position_size_btc,
                'position_size_usdt': position_size_usdt,
                'risk_amount': risk_amount,
                'account_value': account_value
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate position size: {e}")
            return None
    
    def execute_trade(self, signal_data):
        """Execute trade based on signal with mandatory checks"""
        try:
            # Pre-execution security scan
            if not self.pre_execution_security_scan():
                logger.error("🚨 Pre-execution security scan failed")
                return False

            # Check daily trade limit
            if self.daily_trades >= self.max_daily_trades:
                logger.warning(f"⚠️ Daily trade limit reached: {self.daily_trades}/{self.max_daily_trades}")
                return False

            # Check if already in position
            if self.current_position:
                logger.warning("⚠️ Already in position, skipping trade")
                return False

            # Validate signal has probability data for limit orders
            if 'prob_up' not in signal_data or 'prob_down' not in signal_data:
                logger.warning(f"⚠️ Signal missing probability data for limit orders")
                return False

            signal = signal_data['signal']
            current_price = signal_data['price']
            confidence = signal_data['confidence']

            if signal == 'HOLD':
                return False

            # Execute limit order based on grid probabilities
            if signal == 'BUY':
                target_grid = signal_data['target_grid_up']
                trade_result = self.execute_limit_buy_order(current_price, target_grid, signal_data)
            elif signal == 'SELL':
                target_grid = signal_data['target_grid_down']
                trade_result = self.execute_limit_sell_order(current_price, target_grid, signal_data)
            else:
                return False

            if trade_result:
                # Set status for initial limit order notification
                trade_result['status'] = 'PENDING_FILL'

                self.current_position = trade_result
                self.daily_trades += 1

                # Send Telegram notification for limit order placement
                self.send_trade_notification(trade_result)

                logger.info(f"✅ Limit order placed and monitoring started")
                return True
            else:
                logger.error("❌ Failed to execute trade via Binance connector")
                return False

        except Exception as e:
            logger.error(f"❌ Trade execution failed: {e}")
            return False

    def execute_limit_buy_order(self, current_price, target_grid, signal_data):
        """Execute limit buy order at target grid level"""
        try:
            # Calculate order parameters
            entry_price = target_grid  # Place limit order at target grid
            stop_loss_price = entry_price * 0.99   # 1% stop loss
            take_profit_price = entry_price * 1.025  # 2.5% take profit

            # Calculate position size based on 1% risk
            risk_amount = 100.0 * 0.01  # 1% of $100 account
            stop_loss_distance = entry_price - stop_loss_price
            position_size_usd = risk_amount / (stop_loss_distance / entry_price)
            position_size_btc = position_size_usd / entry_price

            logger.info(f"🎯 Placing limit BUY order at grid level ${entry_price:.2f}")
            logger.info(f"📊 Position size: {position_size_btc:.6f} BTC (${position_size_usd:.2f})")
            logger.info(f"🛑 Stop loss: ${stop_loss_price:.2f}")
            logger.info(f"🎯 Take profit: ${take_profit_price:.2f}")

            # Place limit order through Binance connector
            limit_order_result = self.binance_connector.place_limit_order(
                side='BUY',
                quantity=position_size_btc,
                price=entry_price,
                stop_loss_price=stop_loss_price,
                take_profit_price=take_profit_price
            )

            if limit_order_result:
                # Start monitoring in background thread for OCO placement
                import threading
                monitor_thread = threading.Thread(
                    target=self._monitor_limit_order_and_place_oco,
                    args=(limit_order_result,),
                    daemon=True
                )
                monitor_thread.start()

                limit_order_result.update({
                    'signal_data': signal_data,
                    'order_type': 'LIMIT_BUY',
                    'target_grid': target_grid,
                    'monitoring_started': True
                })

            return limit_order_result

        except Exception as e:
            logger.error(f"❌ Limit buy order failed: {e}")
            return None

    def execute_limit_sell_order(self, current_price, target_grid, signal_data):
        """Execute limit sell order at target grid level"""
        try:
            # Calculate order parameters
            entry_price = target_grid  # Place limit order at target grid
            stop_loss_price = entry_price * 1.01   # 1% stop loss
            take_profit_price = entry_price * 0.975  # 2.5% take profit

            # Calculate position size based on 1% risk
            risk_amount = 100.0 * 0.01  # 1% of $100 account
            stop_loss_distance = stop_loss_price - entry_price
            position_size_usd = risk_amount / (stop_loss_distance / entry_price)
            position_size_btc = position_size_usd / entry_price

            logger.info(f"🎯 Placing limit SELL order at grid level ${entry_price:.2f}")
            logger.info(f"📊 Position size: {position_size_btc:.6f} BTC (${position_size_usd:.2f})")
            logger.info(f"🛑 Stop loss: ${stop_loss_price:.2f}")
            logger.info(f"🎯 Take profit: ${take_profit_price:.2f}")

            # Place limit order through Binance connector
            limit_order_result = self.binance_connector.place_limit_order(
                side='SELL',
                quantity=position_size_btc,
                price=entry_price,
                stop_loss_price=stop_loss_price,
                take_profit_price=take_profit_price
            )

            if limit_order_result:
                # Start monitoring in background thread for OCO placement
                import threading
                monitor_thread = threading.Thread(
                    target=self._monitor_limit_order_and_place_oco,
                    args=(limit_order_result,),
                    daemon=True
                )
                monitor_thread.start()

                limit_order_result.update({
                    'signal_data': signal_data,
                    'order_type': 'LIMIT_SELL',
                    'target_grid': target_grid,
                    'monitoring_started': True
                })

            return limit_order_result

        except Exception as e:
            logger.error(f"❌ Limit sell order failed: {e}")
            return None

    def _monitor_limit_order_and_place_oco(self, limit_order_info):
        """Background thread to monitor limit order and place OCO when filled"""
        try:
            logger.info(f"🔍 Starting background monitoring for order {limit_order_info['order_id']}")

            # Monitor the limit order until it fills
            result = self.binance_connector.monitor_and_place_oco_on_fill(limit_order_info)

            if result.get('limit_filled'):
                logger.info(f"✅ Limit order filled and OCO placed successfully")

                # Update current position
                self.current_position = {
                    'order_id': limit_order_info['order_id'],
                    'type': limit_order_info['type'],
                    'entry_price': result.get('fill_price', limit_order_info['entry_price']),
                    'quantity': result.get('fill_quantity', limit_order_info['quantity']),
                    'stop_loss': limit_order_info['stop_loss'],
                    'take_profit': limit_order_info['take_profit'],
                    'oco_order': result.get('oco_order'),
                    'status': 'ACTIVE_WITH_OCO',
                    'timestamp': result.get('timestamp')
                }

                # Send Telegram notification about successful fill and OCO placement
                self.send_trade_notification(self.current_position)

            else:
                logger.warning(f"⚠️ Limit order monitoring failed: {result}")

        except Exception as e:
            logger.error(f"❌ Error in limit order monitoring: {e}")

    def set_stop_loss_take_profit(self, side, quantity, stop_loss_price, take_profit_price):
        """Set stop loss and take profit orders"""
        try:
            # Stop loss order
            if side == 'BUY':
                stop_side = 'SELL'
                tp_side = 'SELL'
            else:
                stop_side = 'BUY'
                tp_side = 'BUY'
            
            # Create stop loss order
            stop_order = self.binance_client.create_margin_order(
                symbol='BTCUSDT',
                side=stop_side,
                type='STOP_LOSS_LIMIT',
                quantity=quantity,
                price=stop_loss_price,
                stopPrice=stop_loss_price,
                timeInForce='GTC',
                isIsolated=True
            )
            
            # Create take profit order
            tp_order = self.binance_client.create_margin_order(
                symbol='BTCUSDT',
                side=tp_side,
                type='LIMIT',
                quantity=quantity,
                price=take_profit_price,
                timeInForce='GTC',
                isIsolated=True
            )
            
            logger.info(f"✅ Stop loss and take profit orders set")
            
        except Exception as e:
            logger.error(f"❌ Failed to set stop loss/take profit: {e}")
    
    def pre_execution_security_scan(self):
        """Mandatory pre-execution security scan"""
        try:
            # Check grid compliance
            if not self.verify_grid_compliance():
                return False
            
            # Check risk parameters
            if not self.verify_risk_parameters():
                return False
            
            # Check account safety
            if not self.verify_account_safety():
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Security scan failed: {e}")
            return False
    
    def verify_grid_compliance(self):
        """Verify grid system compliance"""
        # Grid compliance checks would go here
        return True
    
    def verify_risk_parameters(self):
        """Verify risk management parameters"""
        return (
            self.risk_per_trade == 0.01 and
            self.risk_reward_ratio == 2.5 and
            self.max_daily_trades == 8
        )
    
    def verify_account_safety(self):
        """Verify account safety parameters"""
        balance = self.get_account_balance()
        return balance is not None and balance['total_usdt_value'] > 100
    
    def send_trade_notification(self, position):
        """Send trade notification via Telegram"""
        try:
            # Handle different notification types based on order status
            if position.get('status') == 'PENDING_FILL':
                message = f"""
⏳ **LIMIT ORDER PLACED**
━━━━━━━━━━━━━━━━━━━━
📈 **Order Type:** {position.get('type', 'UNKNOWN')}
🎯 **Target Price:** ${position['entry_price']:.2f}
📊 **Quantity:** {position['quantity']:.6f} BTC
🛑 **Stop Loss:** ${position['stop_loss']:.2f}
🎯 **Take Profit:** ${position['take_profit']:.2f}
⏰ **Time:** {datetime.now().strftime('%H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━
🔍 **Monitoring for fill...**
"""
            elif position.get('status') == 'ACTIVE_WITH_OCO':
                message = f"""
✅ **TRADE EXECUTED + OCO ACTIVE**
━━━━━━━━━━━━━━━━━━━━
📈 **Signal:** {position.get('type', 'UNKNOWN')}
💰 **Fill Price:** ${position['entry_price']:.2f}
🎯 **Take Profit:** ${position['take_profit']:.2f}
🛑 **Stop Loss:** ${position['stop_loss']:.2f}
📊 **Quantity:** {position['quantity']:.6f} BTC
⏰ **Time:** {datetime.now().strftime('%H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━
🛡️ **OCO Orders Active**
"""
            else:
                # Fallback to handle legacy format
                message = f"""
🔔 **TRADE EXECUTED**
━━━━━━━━━━━━━━━━━━━━
📈 **Signal:** {position.get('side', position.get('type', 'UNKNOWN'))}
💰 **Entry:** ${position['entry_price']:.2f}
🎯 **Take Profit:** ${position['take_profit']:.2f}
🛑 **Stop Loss:** ${position['stop_loss']:.2f}
📊 **Quantity:** {position['quantity']:.6f} BTC
⏰ **Time:** {datetime.now().strftime('%H:%M:%S')}
━━━━━━━━━━━━━━━━━━━━
"""
            
            url = f"https://api.telegram.org/bot{self.telegram_config['bot_token']}/sendMessage"
            data = {
                'chat_id': self.telegram_config['chat_id'],
                'text': message,
                'parse_mode': 'HTML'
            }
            
            response = requests.post(url, data=data)
            if response.status_code == 200:
                logger.info("✅ Telegram notification sent")
            else:
                logger.error(f"❌ Failed to send Telegram notification: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ Failed to send Telegram notification: {e}")
    
    def monitor_positions(self):
        """Monitor active positions"""
        while self.running:
            try:
                if self.current_position:
                    # Check if position is still active
                    orders = self.binance_client.get_open_margin_orders(
                        symbol='BTCUSDT',
                        isIsolated=True
                    )
                    
                    if not orders:
                        # Position closed
                        logger.info("✅ Position closed")
                        self.current_position = None
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"❌ Position monitoring error: {e}")
                time.sleep(60)
    
    def start(self):
        """Start the trading engine"""
        self.running = True
        
        # Start position monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_positions)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        logger.info("🚀 Automated Trading Engine started")
        
        # Main trading loop would integrate with signal generator
        while self.running:
            try:
                # This would receive signals from the signal generator
                # For now, just sleep
                time.sleep(60)
                
            except KeyboardInterrupt:
                logger.info("🛑 Trading engine stopped")
                self.running = False
                break
            except Exception as e:
                logger.error(f"❌ Trading engine error: {e}")
                time.sleep(60)

if __name__ == "__main__":
    engine = AutomatedTradingEngine()
    engine.start()
