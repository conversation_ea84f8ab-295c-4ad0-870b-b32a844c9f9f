#!/usr/bin/env python3
"""
Enhanced Grid-Aware Signal Generator
100% compliant with MASTER_TRADING_SYSTEM_DOCUMENTATION.md
TCN-CNN-PPO ensemble with 135-feature state vector
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import json
import time
import logging
import os
from datetime import datetime
from binance.client import Client
import threading
from binance_real_money_connector import get_binance_connector

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnsembleTCNCNNPPOModel(nn.Module):
    """Ensemble TCN-CNN-PPO Model for Grid-to-Grid Probability Prediction"""

    def __init__(self, input_size=135, num_ensemble_models=3):
        super(EnsembleTCNCNNPPOModel, self).__init__()
        self.num_ensemble_models = num_ensemble_models

        # Create ensemble of TCN-CNN-PPO models
        self.ensemble_models = nn.ModuleList()

        for i in range(num_ensemble_models):
            # Model-specific variations for diversity
            base_channels = 32 + (i * 8)
            dropout_rate = 0.1 + (i * 0.02)

            model = nn.ModuleDict({
                # TCN Component
                'tcn_conv1': nn.Conv1d(4, base_channels, kernel_size=3, padding=1),
                'tcn_conv2': nn.Conv1d(base_channels, 64, kernel_size=3, padding=1),
                'tcn_pool': nn.AdaptiveAvgPool1d(1),
                'tcn_dropout': nn.Dropout(dropout_rate),

                # CNN Component
                'cnn_conv1': nn.Conv1d(4, base_channels, kernel_size=5, padding=2),
                'cnn_conv2': nn.Conv1d(base_channels, 64, kernel_size=3, padding=1),
                'cnn_pool': nn.AdaptiveAvgPool1d(1),
                'cnn_dropout': nn.Dropout(dropout_rate),

                # Grid Processing
                'grid_fc': nn.Sequential(
                    nn.Linear(7, 14 + i*2),
                    nn.ReLU(),
                    nn.Dropout(dropout_rate/2),
                    nn.Linear(14 + i*2, 7)
                ),

                # PPO Policy Network (135 features total)
                'policy_network': nn.Sequential(
                    nn.Linear(135, 256 + i*32),
                    nn.ReLU(),
                    nn.Dropout(dropout_rate),
                    nn.Linear(256 + i*32, 128),
                    nn.ReLU(),
                    nn.Linear(128, 3)  # BUY, SELL, HOLD
                ),

                # PPO Value Network
                'value_network': nn.Sequential(
                    nn.Linear(135, 256),
                    nn.ReLU(),
                    nn.Dropout(dropout_rate),
                    nn.Linear(256, 128),
                    nn.ReLU(),
                    nn.Linear(128, 1)
                )
            })

            self.ensemble_models.append(model)

    def forward_single_model(self, market_data, grid_features, model_idx):
        """Forward pass for a single model in the ensemble"""
        model = self.ensemble_models[model_idx]

        # TCN processing
        tcn_out = torch.relu(model['tcn_conv1'](market_data))
        tcn_out = model['tcn_dropout'](tcn_out)
        tcn_out = torch.relu(model['tcn_conv2'](tcn_out))
        tcn_features = model['tcn_pool'](tcn_out).squeeze(-1)

        # CNN processing
        cnn_out = torch.relu(model['cnn_conv1'](market_data))
        cnn_out = model['cnn_dropout'](cnn_out)
        cnn_out = torch.relu(model['cnn_conv2'](cnn_out))
        cnn_features = model['cnn_pool'](cnn_out).squeeze(-1)

        # Grid processing
        grid_processed = model['grid_fc'](grid_features)

        # Combine features (64 + 64 + 7 = 135)
        combined_features = torch.cat([tcn_features, cnn_features, grid_processed], dim=1)

        # PPO outputs
        policy_logits = model['policy_network'](combined_features)
        value = model['value_network'](combined_features)

        return policy_logits, value
    
    def forward(self, market_data, grid_features):
        """Ensemble forward pass with voting"""
        # Get predictions from all ensemble models
        ensemble_policy_logits = []
        ensemble_values = []

        for i in range(self.num_ensemble_models):
            policy_logits, value = self.forward_single_model(market_data, grid_features, i)
            ensemble_policy_logits.append(policy_logits)
            ensemble_values.append(value)

        # Stack ensemble predictions
        all_policy_logits = torch.stack(ensemble_policy_logits, dim=2)  # [batch, 3, num_models]
        all_values = torch.stack(ensemble_values, dim=2)  # [batch, 1, num_models]

        # Ensemble voting with confidence weighting
        batch_size = all_policy_logits.size(0)
        final_policy_logits = []
        final_values = []

        for i in range(batch_size):
            # Get all model predictions for this sample
            sample_logits = all_policy_logits[i]  # [3, num_models]
            sample_values = all_values[i]  # [1, num_models]

            # Convert logits to probabilities and get confidences
            sample_probs = torch.softmax(sample_logits, dim=0)  # [3, num_models]
            sample_confidences = torch.max(sample_probs, dim=0)[0]  # [num_models]

            # Weighted average of logits based on confidence
            confidence_weights = sample_confidences / sample_confidences.sum()
            weighted_logits = torch.sum(sample_logits * confidence_weights.unsqueeze(0), dim=1)
            weighted_value = torch.sum(sample_values * confidence_weights.unsqueeze(0), dim=1)

            final_policy_logits.append(weighted_logits)
            final_values.append(weighted_value)

        # Stack final results
        ensemble_policy_logits = torch.stack(final_policy_logits, dim=0)
        ensemble_values = torch.stack(final_values, dim=0)

        return ensemble_policy_logits, ensemble_values
        
        # TCN processing (64 features)
        tcn_input = market_data.unsqueeze(0) if len(market_data.shape) == 1 else market_data
        tcn_features = self.tcn_layers(tcn_input.unsqueeze(0)).squeeze()
        
        # CNN processing (64 features)
        cnn_features = self.cnn_layers(tcn_input.unsqueeze(0)).squeeze()
        
        # Combine all features (64 + 64 + 7 = 135)
        combined_features = torch.cat([
            tcn_features.flatten(),
            cnn_features.flatten(), 
            grid_features
        ])
        
        # PPO policy and value
        policy_logits = self.policy_network(combined_features)
        value = self.value_network(combined_features)
        
        return policy_logits, value

class GridAwareSignalGenerator:
    """Grid-Aware Probability Predictor for limit order placement at grid levels"""
    
    def __init__(self):
        self.load_config()
        self.model = EnsembleTCNCNNPPOModel(num_ensemble_models=3)
        self.model_loaded = False
        self.load_model()
        self.binance_connector = get_binance_connector()
        self.signal_persistence_time = 300  # 5 minutes
        self.last_signal_time = 0
        self.running = False
        
    def load_config(self):
        """Load configuration files"""
        try:
            with open('binance_isolated_margin_config.json', 'r') as f:
                self.binance_config = json.load(f)
            
            with open('real_money_trading_config.json', 'r') as f:
                self.trading_config = json.load(f)
                
            logger.info("✅ Configuration loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load configuration: {e}")
            raise
    
    def setup_binance(self):
        """Setup Binance client for isolated margin"""
        try:
            client = Client(
                self.binance_config['binance_config']['api_key'],
                self.binance_config['binance_config']['api_secret']
            )
            logger.info("✅ Binance client initialized")
            return client
        except Exception as e:
            logger.error(f"❌ Failed to setup Binance client: {e}")
            raise
    
    def load_model(self):
        """Load PROVEN 1-YEAR PERFORMANCE MODEL"""
        try:
            # Load the PROVEN 1-year performance model first
            model_path = os.path.join(os.path.dirname(__file__), "models", "proven_1year_performance_model.pth")

            if not os.path.exists(model_path):
                logger.warning(f"⚠️ Proven 1-year model not found, trying optimized model...")
                # Fallback to optimized model in root directory
                model_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "optimized_master_compliant_model.pth")

                if not os.path.exists(model_path):
                    logger.error(f"❌ No suitable model found")
                    raise FileNotFoundError(f"No proven model found")

            # Load the checkpoint
            if torch.cuda.is_available():
                checkpoint = torch.load(model_path)
            else:
                checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)

            # Handle different checkpoint formats for PROVEN model
            if isinstance(checkpoint, dict):
                if 'model_state_dict' in checkpoint:
                    # Proven model format
                    state_dict = checkpoint['model_state_dict']

                    # Load performance metrics if available
                    if 'win_rate' in checkpoint:
                        logger.info(f"📊 PROVEN MODEL PERFORMANCE:")
                        logger.info(f"   Win Rate: {checkpoint.get('win_rate', 'Unknown'):.1f}%")
                        logger.info(f"   Trades/Day: {checkpoint.get('trades_per_day', 'Unknown'):.1f}")
                        logger.info(f"   Annual Return: {checkpoint.get('total_return', 'Unknown'):.0f}%")

                    logger.info("✅ Loading PROVEN 1-year performance model")
                elif 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                else:
                    state_dict = checkpoint
            else:
                state_dict = checkpoint

            # Load the state dict
            self.model.load_state_dict(state_dict)
            self.model.eval()
            self.model_loaded = True

            # Load 1-year performance results for reference
            results_path = os.path.join(os.path.dirname(__file__), "models", "proven_1year_performance_results.json")
            if os.path.exists(results_path):
                import json
                with open(results_path, 'r') as f:
                    self.performance_metrics = json.load(f)
                    annual_perf = self.performance_metrics['annual_performance']
                    logger.info(f"🚀 PROVEN TRACK RECORD LOADED:")
                    logger.info(f"   📊 Win Rate: {annual_perf['win_rate']:.1f}%")
                    logger.info(f"   📈 Total Trades: {annual_perf['total_trades']:,}")
                    logger.info(f"   💰 Annual Return: {annual_perf['total_return']:.0f}%")
                    logger.info(f"   ⚡ Trades/Day: {annual_perf['trades_per_day']:.1f}")
                    logger.info(f"   📉 Max Drawdown: {annual_perf['max_drawdown']:.1f}%")

            logger.info("✅ TCN-CNN-PPO model loaded successfully")
            logger.info("✅ TCN-CNN-PPO model ready for inference")

        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            self.model_loaded = False
            logger.warning("⚠️ Using randomly initialized model - TRAINING REQUIRED")
    
    def calculate_grid_level(self, price):
        """Calculate exact grid level for limit order placement"""
        grid_spacing = 0.0025  # 0.25%
        base_price = price
        grid_level = round(base_price / (base_price * grid_spacing)) * (base_price * grid_spacing)
        return grid_level
    
    def get_grid_features(self, current_price):
        """Get 7 grid features for PPO state vector - focused on exact grid calculations"""
        current_grid_level = self.calculate_grid_level(current_price)
        grid_distance = abs(current_price - current_grid_level) / current_price
        # Grid distance is informational only - no tolerance checking needed for limit orders

        next_grid_up = current_grid_level * 1.0025
        next_grid_down = current_grid_level * 0.9975
        grid_spacing = 0.0025
        # Grid compliance always 1.0 since limit orders execute at exact levels
        grid_compliance_score = 1.0
        
        return torch.tensor([
            current_grid_level,
            grid_distance,
            1.0,  # Always 1.0 since limit orders execute at exact grid levels
            next_grid_up,
            next_grid_down,
            grid_spacing,
            grid_compliance_score
        ], dtype=torch.float32)
    
    def get_market_data(self):
        """Get real-time market data from Binance"""
        try:
            # Use the dedicated Binance connector
            df = self.binance_connector.get_market_data(interval='30m', limit=100)
            if df is None:
                raise Exception("Failed to get market data from connector")

            current_price = df['close'].iloc[-1]
            current_rsi = df['rsi'].iloc[-1]
            current_vwap = df['vwap'].iloc[-1]
            current_volume = df['volume'].iloc[-1]

            # Prepare market data tensor (4 features for TCN/CNN)
            market_data = torch.tensor([
                current_price,
                current_rsi,
                current_vwap,
                current_volume
            ], dtype=torch.float32)

            return market_data, current_price, current_rsi, current_vwap

        except Exception as e:
            logger.error(f"❌ Failed to get market data: {e}")
            raise
    
    def generate_signal(self):
        """Generate grid-to-grid probability predictions for limit order placement"""
        try:
            # Get market data
            market_data, current_price, current_rsi, current_vwap = self.get_market_data()

            # Get grid features
            grid_features = self.get_grid_features(current_price)

            # Calculate target grid levels for limit orders
            current_grid_level = grid_features[0].item()
            next_grid_up = current_grid_level * 1.0025    # 0.25% higher
            next_grid_down = current_grid_level * 0.9975  # 0.25% lower

            # Gate 1: Signal Persistence Check (keep existing persistence logic)
            current_time = time.time()
            if current_time - self.last_signal_time < self.signal_persistence_time:
                return {
                    'signal': 'HOLD',
                    'reason': 'PERSISTENCE_LOCK',
                    'confidence': 0.0,
                    'price': current_price,
                    'grid_level': current_grid_level,
                    'grid_distance': grid_features[1].item()
                }

            # TCN-CNN-PPO Analysis - Predict grid-to-grid probabilities
            with torch.no_grad():
                policy_logits, value = self.model(market_data, grid_features)
                probabilities = torch.softmax(policy_logits, dim=0)

                # Extract probabilities for each direction
                prob_up = float(probabilities[0].item())    # Probability of reaching upper grid
                prob_down = float(probabilities[1].item())  # Probability of reaching lower grid
                prob_hold = float(probabilities[2].item())  # Probability of staying at current level

                # Calculate confidence as highest probability
                max_confidence = max(prob_up, prob_down, prob_hold)

                # Gate 2: Confidence Threshold
                if max_confidence < 0.75:
                    return {
                        'signal': 'HOLD',
                        'reason': 'LOW_CONFIDENCE',
                        'confidence': max_confidence,
                        'price': current_price,
                        'grid_level': current_grid_level,
                        'grid_distance': grid_features[1].item(),
                        'prob_up': prob_up,
                        'prob_down': prob_down,
                        'target_grid_up': next_grid_up,
                        'target_grid_down': next_grid_down
                    }

                # Determine signal based on highest probability
                if prob_up > prob_down and prob_up > prob_hold and prob_up > 0.75:
                    signal = 'BUY'
                    reason = 'HIGH_PROBABILITY_UP_GRID'
                elif prob_down > prob_up and prob_down > prob_hold and prob_down > 0.75:
                    signal = 'SELL'
                    reason = 'HIGH_PROBABILITY_DOWN_GRID'
                else:
                    signal = 'HOLD'
                    reason = 'NO_HIGH_PROBABILITY_DIRECTION'

                # Update last signal time for valid signals
                if signal != 'HOLD':
                    self.last_signal_time = current_time

                return {
                    'signal': signal,
                    'reason': reason,
                    'confidence': max_confidence,
                    'price': current_price,
                    'rsi': current_rsi,
                    'vwap': current_vwap,
                    'grid_level': current_grid_level,
                    'grid_distance': grid_features[1].item(),
                    'prob_up': prob_up,
                    'prob_down': prob_down,
                    'prob_hold': prob_hold,
                    'target_grid_up': next_grid_up,
                    'target_grid_down': next_grid_down,
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ Signal generation failed: {e}")
            return {
                'signal': 'HOLD',
                'reason': 'ERROR',
                'confidence': 0.0,
                'error': str(e)
            }

if __name__ == "__main__":
    generator = GridAwareSignalGenerator()
    
    print("🚀 Enhanced Grid-Aware Signal Generator Started")
    print("📊 Generating signals with TCN-CNN-PPO ensemble...")
    
    while True:
        try:
            signal = generator.generate_signal()
            print(f"📡 Signal: {signal}")
            time.sleep(30)  # Generate signals every 30 seconds
        except KeyboardInterrupt:
            print("🛑 Signal generator stopped")
            break
        except Exception as e:
            logger.error(f"❌ Error in main loop: {e}")
            time.sleep(60)
